/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AllView: typeof import('./src/components/view/all/AllView.vue')['default']
    AppointmentModal: typeof import('./src/components/module/appointment-modal/AppointmentModal.vue')['default']
    AuthModal: typeof import('./src/components/auth/AuthModal.vue')['default']
    BasketballData: typeof import('./src/components/module/bb-data/BasketballData.vue')['default']
    BasketballResult: typeof import('./src/components/module/bb-result/BasketballResult.vue')['default']
    ChatView: typeof import('./src/components/module/chat/ChatView.vue')['default']
    DataIcon: typeof import('./src/components/module/data-icon/index.vue')['default']
    DefaultLogo: typeof import('./src/components/module/default-logo/index.vue')['default']
    DetailView: typeof import('./src/components/view/detail/DetailView.vue')['default']
    EventLine: typeof import('./src/components/module/event-line/index.vue')['default']
    FooterView: typeof import('./src/components/layout/FooterView.vue')['default']
    GiftRankView: typeof import('./src/components/module/gift-rank/GiftRankView.vue')['default']
    GlobalModifyPassword: typeof import('./src/components/view/user/GlobalModifyPassword.vue')['default']
    GlobalView: typeof import('./src/components/global/GlobalView.vue')['default']
    HeaderView: typeof import('./src/components/layout/HeaderView.vue')['default']
    HomePageView: typeof import('./src/components/view/home/<USER>')['default']
    HomePageView1: typeof import('./src/components/view/home/<USER>')['default']
    Level: typeof import('./src/components/module/level/Level.vue')['default']
    LineUp: typeof import('./src/components/module/line-up/index.vue')['default']
    LiveAppointments: typeof import('./src/components/view/user/LiveAppointments.vue')['default']
    LiveDataView: typeof import('./src/components/module/live-data/LiveDataView.vue')['default']
    LiveItem: typeof import('./src/components/module/live-item/LiveItem.vue')['default']
    LiveItemView: typeof import('./src/components/module/live-item/LiveItemView.vue')['default']
    LiveSettings: typeof import('./src/components/view/user/LiveSettings.vue')['default']
    LoginModal: typeof import('./src/components/auth/LoginModal.vue')['default']
    ModifyPassword: typeof import('./src/components/view/user/ModifyPassword.vue')['default']
    ModifyPhone: typeof import('./src/components/view/user/ModifyPhone.vue')['default']
    NButton: typeof import('naive-ui')['NButton']
    NCard: typeof import('naive-ui')['NCard']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NDatePicker: typeof import('naive-ui')['NDatePicker']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NInput: typeof import('naive-ui')['NInput']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NoneBox: typeof import('./src/components/module/none-box/index.vue')['default']
    NotFoundView: typeof import('./src/components/view/404/NotFoundView.vue')['default']
    NPopconfirm: typeof import('naive-ui')['NPopconfirm']
    NPopover: typeof import('naive-ui')['NPopover']
    NProgress: typeof import('naive-ui')['NProgress']
    NResult: typeof import('naive-ui')['NResult']
    NSelect: typeof import('naive-ui')['NSelect']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    NUpload: typeof import('naive-ui')['NUpload']
    RankView: typeof import('./src/components/module/gift-rank/RankView.vue')['default']
    RegisterModal: typeof import('./src/components/auth/RegisterModal.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScheduleView: typeof import('./src/components/view/schedule/scheduleView.vue')['default']
    StartIconView: typeof import('./src/components/module/start-icon/StartIconView.vue')['default']
    UserAppointments: typeof import('./src/components/view/user/UserAppointments.vue')['default']
    UserCenterView: typeof import('./src/components/view/user/UserCenterView.vue')['default']
    UserDashboard: typeof import('./src/components/view/user/UserDashboard.vue')['default']
    UserFeedback: typeof import('./src/components/view/user/UserFeedback.vue')['default']
    UserFollows: typeof import('./src/components/view/user/UserFollows.vue')['default']
    UserMessages: typeof import('./src/components/view/user/UserMessages.vue')['default']
    UserProfile: typeof import('./src/components/view/user/UserProfile.vue')['default']
  }
}
