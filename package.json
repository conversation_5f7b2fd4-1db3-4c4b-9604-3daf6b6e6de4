{"name": "redbookgpt", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "dev:test": "vite --mode test", "dev:prod": "vite --mode production", "build": "run-p type-check build-only", "build:test": "run-p type-check build-only:test", "build:prod": "run-p type-check build-only:prod", "preview": "vite preview", "build-only": "vite build", "build-only:test": "vite build --mode test", "build-only:prod": "vite build --mode production", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "generate-proto": "pbjs -t static-module -w es6 -o ./src/proto/proto.js ./protos/*.proto && pbts ./src/proto/proto.js -o ./src/proto/proto.d.ts"}, "dependencies": {"axios": "^1.4.0", "c-scrollbar": "^1.0.2", "compressorjs": "^1.2.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.8", "dplayer": "^1.27.1", "hls.js": "^1.6.5", "normalize.css": "^8.0.1", "pinia": "^2.0.36", "pinia-plugin-persistedstate": "^3.2.1", "protobufjs": "^7.2.4", "swiper": "^11.2.8", "vue": "^3.3.2", "vue-qr": "^4.0.9", "vue-router": "^4.2.0", "weixin-js-sdk": "^1.6.0", "ws": "^8.13.0", "xgplayer": "^3.0.22", "xgplayer-flv": "^3.0.22", "xgplayer-hls": "^3.0.22"}, "devDependencies": {"@rushstack/eslint-patch": "^1.2.0", "@tsconfig/node18": "^2.0.1", "@types/node": "^18.16.8", "@vicons/fluent": "^0.12.0", "@vicons/ionicons4": "^0.12.0", "@vicons/ionicons5": "^0.12.0", "@vitejs/plugin-vue": "^4.2.3", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/tsconfig": "^0.4.0", "eslint": "^8.39.0", "eslint-plugin-vue": "^9.11.0", "naive-ui": "^2.34.3", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "protobufjs-cli": "^1.1.1", "sass": "^1.89.2", "typescript": "~5.0.4", "unplugin-auto-import": "^0.16.0", "unplugin-vue-components": "^0.24.1", "vfonts": "^0.0.3", "vite": "^4.3.5", "vue-tsc": "^1.6.4", "vue-wxlogin": "^1.0.4"}}