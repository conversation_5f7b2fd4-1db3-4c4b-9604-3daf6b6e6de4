<template>
  <!--  -->
  <div v-if="homeLineup.length > 0 || homeReplace.length > 0 || guestReplace.length > 0 || injuryList.length > 0">
    <!-- 首发title -->
    <div class="fistLine-title" v-if="homeLineup.length > 0">
      <div class="home">
        <div class="coach-info" v-if="homeCoachName">
          <div class="top">
            <div class="coach-name" v-if="homeCoachName">教练: {{ homeCoachName }}</div>
          </div>
          <div class="bottom">
            <div class="formation" v-if="homeFormation">阵型: {{ homeFormation }}</div>
          </div>
        </div>
      </div>
      <div class="away">
        <div class="coach-info" v-if="awayCoachName">
          <div class="top">
            <div class="coach-name" v-if="awayCoachName">教练: {{ awayCoachName }}</div>
          </div>
          <div class="bottom">
            <div class="formation" v-if="awayFormation">阵型: {{ awayFormation }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 首发阵容 -->
    <div class="lineup-box" v-if="homeLineup.length > 0">
      <div class="referee-box">
        <div class="name">裁判 : {{ refereeName ? refereeName : '暂无裁判信息' }}</div>
      </div>
      <div class="teams home-teams">
        <div class="player-box" v-for="item in homeLineup" :style="calculatePlayerPosition(item, 'home')">
          <div class="player-logo">
            <!-- <img class="icon" :src="item.playerLogo || defaultPlayerLogo" /> -->
            <defaultLogo type="player" :logo="item.playerLogo"></defaultLogo>
            <div class="number" v-if="item.shirtNumber">{{ item.shirtNumber }}</div>
          </div>
          <div class="player-name">
            <div class="name">{{ item.playerName || '' }}</div>
          </div>
        </div>
      </div>
      <div class="teams away-teams">
        <div class="player-box" v-for="item in awayLineup" :style="calculatePlayerPosition(item, 'away')">
          <div class="player-logo">
            <!-- <img class="icon" :src="item.playerLogo || defaultPlayerLogo" /> -->
            <defaultLogo type="player" :logo="item.playerLogo"></defaultLogo>
            <div class="number" v-if="item.shirtNumber">{{ item.shirtNumber }}</div>
          </div>
          <div class="player-name">
            <div class="name">{{ item.playerName }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div v-else class="none-box">
      <none-box text="暂无首发信息" />
    </div> -->
    <!-- 替补阵容 -->
    <div class="Substitution-box" v-if="homeReplace.length > 0 || guestReplace.length > 0">
      <div class="title-box">
        <div class="team-logo">
          <defaultLogo type="team" :logo="homeLogo"></defaultLogo>
        </div>
        <div class="center">替补阵容</div>
        <div class="team-logo">
          <defaultLogo type="team" :logo="awayLogo"></defaultLogo>
        </div>
      </div>
      <div class="lineup-list">
        <div class="lineup-items home">
          <div class="item" v-for="item in homeReplace" :key="item.shirtNumber">
            <div class="player-logo" @click="onShowPlayerPopup(item)">
              <!-- <img class="icon" :src="item.playerLogo || defaultPlayerLogo" /> -->
              <defaultLogo type="player" :logo="item.playerLogo"></defaultLogo>
              <div class="number" v-if="item.shirtNumber">{{ item.shirtNumber }}</div>
            </div>
            <div class="player-info">
              <div class="top">
                <div class="player-name">{{ item.playerName }}</div>
              </div>
              <div class="bottom">
                <div class="player-position">{{ getPositionName(item.position) }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="lineup-items away">
          <div class="item" v-for="item in guestReplace" :key="item.shirtNumber">
            <div class="player-info">
              <div class="top">
                <div class="player-name">{{ item.playerName }}</div>
              </div>
              <div class="bottom">
                <div class="player-position">{{ getPositionName(item.position) }}</div>
              </div>
            </div>
            <div class="player-logo">
              <!-- <img class="icon" :src="item.playerLogo || defaultPlayerLogo" /> -->
              <defaultLogo type="player" :logo="item.playerLogo"></defaultLogo>
              <div class="number" v-if="item.shirtNumber">{{ item.shirtNumber }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="Substitution-box" v-if="injuryList && injuryList.length > 0">
      <div class="title-box">
        <div class="team-logo">
          <defaultLogo type="team" :logo="homeLogo"></defaultLogo>
        </div>
        <div class="center">伤停信息</div>
        <div class="team-logo">
          <defaultLogo type="team" :logo="awayLogo"></defaultLogo>
        </div>
      </div>
      <div class="lineup-list">
        <div class="lineup-items home">
          <template v-for="item in injuryList" :key="item.id">
            <div class="item" v-if="item && item.type === 1">
              <div class="player-logo">
                <!-- <img class="icon" :src="item.playerLogo || defaultPlayerLogo" /> -->
                <defaultLogo type="player" :logo="item.playerLogo"></defaultLogo>
                <div class="number" v-if="item.shirtNumber">{{ item.shirtNumber }}</div>
              </div>
              <div class="player-info">
                <div class="top">
                  <div class="player-name">{{ item.playerName }}</div>
                </div>
                <div class="bottom">
                  <div class="player-position">{{ getPositionName(item.position) }}</div>
                </div>
              </div>
            </div>
          </template>
        </div>
        <div class="lineup-items away">
          <template v-for="item in injuryList" :key="item.id">
            <div class="item" v-if="item && item.type === 2">
              <div class="player-info">
                <div class="top">
                  <div class="player-name">{{ item.playerName }}</div>
                </div>
                <div class="bottom">
                  <div class="player-position">{{ getPositionName(item.position) }}</div>
                </div>
              </div>
              <div class="player-logo">
                <!-- <img class="icon" :src="item.playerLogo || defaultPlayerLogo" /> -->
                <defaultLogo type="player" :logo="item.playerLogo"></defaultLogo>
                <div class="number" v-if="item.shirtNumber">{{ item.shirtNumber }}</div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
  <noneBox v-else text="暂无阵容信息"></noneBox>
</template>
<script setup>

import { getLineupApi, getInjuryApi } from '@/api/match'
import { ref, onMounted } from 'vue';
import noneBox from '@/components/module/none-box/index.vue'
// import defaultPlayerLogo from '@/static/index/default-player-logo.png'
import defaultLogo from '@/components/module/default-logo/index.vue';

const props = defineProps({
  matchId: {
    type: Number,
    default: 0
  },
  homeLogo: {
    type: String,
    default: ''
  },
  awayLogo: {
    type: String,
    default: ''
  }
})
//获取阵容
//主场首发
const homeLineup = ref([])
//客场首发
const awayLineup = ref([])
const homeCoachName = ref('')
const awayCoachName = ref('')
//客队阵型
const awayFormation = ref('')
//主队阵型
const homeFormation = ref('')
//替补
const guestReplace = ref([])
//主队替补
const homeReplace = ref([])
//裁判名称
const refereeName = ref('')
//主队换人
// const homeSubstitution = ref([])
//客队换人
// const guestSubstitution = ref([])
const getLineup = async () => {
  let res = await getLineupApi({ matchId: props.matchId })
  if (res.data) {
    if (res.data?.homeLineup && res.data?.homeLineup.length > 0) {
      homeLineup.value = res.data?.homeLineup
    }
    if (res.data?.awayLineup && res.data?.awayLineup.length > 0) {
      awayLineup.value = res.data?.awayLineup
    }
    homeCoachName.value = res.data.homeCoachName;
    awayCoachName.value = res.data.awayCoachName;
    awayFormation.value = res.data.awayFormation;
    homeFormation.value = res.data.homeFormation;
    guestReplace.value = res.data.guestReplace;
    homeReplace.value = res.data.homeReplace;
    refereeName.value = res.data.refereeName;
  }
}
const screenWidth = ref(1200);
const iconWidth = ref(40);
const calculatePlayerPosition = (player, teamType) => {
  const ballMapWidth = screenWidth.value / 2; // 每个队伍占一半宽度
  const ballMapHeight = 500;
  const unitWidth = ballMapWidth / 100; // 每个队伍区域的单位宽度
  const unitHeight = ballMapHeight / 100; // 高度单位

  if (teamType === 'home') {
    // 主队在左边，将原来的y坐标映射到x轴，x坐标映射到y轴
    return {
      left: (Math.floor(player.ycoordinate * unitWidth) - iconWidth.value / 2) + 'px',
      top: (Math.floor((100 - player.xcoordinate) * unitHeight) - iconWidth.value / 2) + 'px',
      position: 'absolute',
    };
  } else if (teamType === 'away') {
    // 客队在右边，y坐标镜像后映射到x轴，x坐标映射到y轴
    return {
      left: (Math.floor((100 - player.ycoordinate) * unitWidth) - iconWidth.value / 2) + 'px',
      top: (Math.floor(player.xcoordinate * unitHeight) - iconWidth.value / 2) + 'px',
      position: 'absolute',
    };
  }
};
// F-前锋、M-中场、D-后卫、G-守门员、其他为未知
const positionMap = {
  F: '前锋',
  M: '中场',
  D: '后卫',
  G: '守门员',
};
const getPositionName = (position) => {
  return positionMap[position] || '未知';
};
//获取伤停
let injuryList = ref([])
const getInjury = async () => {
  try {
    let res = await getInjuryApi({ matchId: props.matchId })
    // 确保返回的数据是数组格式
    injuryList.value = Array.isArray(res.data) ? res.data : []
  } catch (error) {
    console.error('获取伤停信息失败:', error)
    injuryList.value = []
  }
}
onMounted(() => {
  getLineup()
  getInjury()
})
</script>
<style scoped lang="scss">
.none-box {
  background-color: #fff;
}

.Substitution-box {
  background-color: #fff;
  padding: 12px;
  // border-radius: $bf-public-size-16;

  .title-box {
    border-radius: 16px;
    padding: 4px 0;
    display: flex;
    justify-content: space-around;
    border: 1px solid #D9D9D9;

    .team-logo {
      width: 30px;
      height: 30px;

      .icon {
        display: block;
        width: 30px;
        height: 30px;
      }
    }

    .center {
      color: #FB2B1F;
      font-size: 18px;
      font-weight: bold;
    }
  }

  .lineup-list {
    display: flex;
    margin-top: 12px;

    .lineup-items {
      width: 50%;

      &.home {
        border-right: 1px solid rgba(129, 129, 129, 0.2);

        .item {
          border-top-left-radius: 4px;
          border-bottom-left-radius: 4px;

          .number {
            background-color: #FB2B1F;
          }

          .player-info {
            padding-left: 8px;
          }
        }
      }

      &.away {
        .item {
          border-top-right-radius: 4px;
          border-bottom-right-radius: 4px;

          .number {
            background-color: #3177FD;
          }

          .player-info {
            padding-right: 8px;

            .top {

              .player-name {
                text-align: right;
                flex: 1;
              }
            }

            .bottom {
              .player-position {
                text-align: right;
                flex: 1;
              }

            }
          }
        }
      }

      .item {
        display: flex;
        background-color: #F4F4F4;
        padding: 8px;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .player-logo {
          position: relative;
          background-color: #fff;
          border: 2px solid #fff;
          box-sizing: border-box;
          padding: 1px;
          border-radius: 50%;
          width: fit-content;
          height: fit-content;
          flex-shrink: 0;
          width: 60px;
          height: 60px;

          .icon {
            width: 60px;
            height: 60px;
            display: block;
          }

          .number {
            font-size: 10px;
            position: absolute;
            border-radius: 50%;
            color: #fff;
            border: 1px solid #fff;
            padding: 2px;
            width: 20px;
            height: 20px;
            display: flex;
            line-height: 1;
            justify-content: center;
            align-items: center;
            top: -3px;
            left: -3px;
          }
        }

        .player-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-around;

          .top {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .player-name {
              // width: 70px;
              font-size: 16px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

          }

          .bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .player-position {
              color: #818181;
              font-size: 16px;

            }

          }
        }
      }

    }
  }
}

.lineup-box {
  width: 100%;
  height: 500px;
  background-image: url('@/static/details/line-up-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  position: relative;

  .referee-box {
    position: absolute;
    width: 100%;
    color: #fff;
    font-size: 14px;
    text-align: center;
    top: -40px;
    // transform: translateY(-50%);
    z-index: 9;
  }

  .teams {
    position: absolute;
    top: 0;
    width: 50%;
    height: 100%;

    .player-box {
      position: absolute;
      width: fit-content;
      height: fit-content;
    }

    .player-logo {
      position: relative;
      background-color: #fff;
      padding: 1px;
      border-radius: 50%;
      width: fit-content;
      height: fit-content;
      flex-shrink: 0;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.25);
      width: 40px;
      height: 40px;

      .icon {
        width: 40px;
        height: 40px;
        display: block;
        border-radius: 50%;
      }

      .number {
        font-size: 10px;
        position: absolute;
        border-radius: 50%;
        color: #fff;
        border: 1px solid #fff;
        width: 20px;
        height: 20px;
        display: flex;
        line-height: 1;
        justify-content: center;
        align-items: center;
        left: -18px;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .player-name {
      color: #fff;
      display: flex;
      align-items: center;
      position: absolute;
      bottom: initial;
      transform: translateX(-50%);
      left: 50%;

      .name {
        font-size: 14px;
        max-width: 90px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .home-teams {
    left: 0;

    .number {
      background-color: #FB2B1F;
    }
  }

  .away-teams {
    right: 0;

    .number {
      background-color: #3177FD;
    }
  }
}

.fistLine-title {
  height: 60px;
  background: #76C97E;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40px;

  .coach-info {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    color: #fff;

    .coach-name {
      font-size: 16px;
    }

    .formation {
      font-size: 14px;
    }
  }
}
</style>