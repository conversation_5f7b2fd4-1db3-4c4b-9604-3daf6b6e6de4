<template>
  <div class="live-data-view">
    <!-- 进球分布 -->
    <div class="data-section" v-if="distributionData.home.disAll">
      <div class="section-header">
        <div class="red-line"></div>
        <span class="section-title">进球分布</span>
      </div>
      <div class="goal-distribution">
        <div class="team-data">
          <div class="team-header">
            <img class="team-logo" :src="liveDetail.homeLogo" />
            <span class="team-name">{{ liveDetail.homeName }}</span>
          </div>
          <div class="time-periods">
            <div class="period-layout">
              <div class="label-column"></div>
              <div class="period-header">
                <span>0-15'</span>
                <span>15-30'</span>
                <span>30-45'</span>
                <span>45-60'</span>
                <span>60-75'</span>
                <span>75-90'</span>
              </div>
            </div>
            <div class="period-data" v-if="distributionData.home.disAll">
              <div class="period-item">
                <span class="label">总</span>
                <span class="value" v-for="(item, index) in distributionData.home.disAll.split(',')" :key="index"
                  :class="{ red: isMaxValue(item, distributionData.home.disAll.split(',')) }">
                  {{ item }}
                </span>
              </div>
              <div class="period-item">
                <span class="label">主</span>
                <span class="value" v-for="(item, index) in distributionData.home.disHome.split(',')" :key="index"
                  :class="{ red: isMaxValue(item, distributionData.home.disHome.split(',')) }">
                  {{ item }}
                </span>
              </div>
              <div class="period-item">
                <span class="label">客</span>
                <span class="value" v-for="(item, index) in distributionData.home.disAway.split(',')" :key="index"
                  :class="{ red: isMaxValue(item, distributionData.home.disAway.split(',')) }">
                  {{ item }}
                </span>
              </div>
            </div>
            <noneBox v-else text="暂无数据" :isIocn="false"></noneBox>
          </div>
        </div>
        <div class="team-data">
          <div class="team-header">
            <img class="team-logo" :src="liveDetail.awayLogo" />
            <span class="team-name">{{ liveDetail.awayName }}</span>
          </div>
          <div class="time-periods">
            <div class="period-layout">
              <div class="label-column"></div>
              <div class="period-header">
                <span>0-15'</span>
                <span>15-30'</span>
                <span>30-45'</span>
                <span>45-60'</span>
                <span>60-75'</span>
                <span>75-90'</span>
              </div>
            </div>
            <div class="period-data" v-if="distributionData.away.disAll">
              <div class="period-item">
                <span class="label">总</span>
                <span class="value" v-for="(item, index) in distributionData.away.disAll.split(',')" :key="index"
                  :class="{ red: isMaxValue(item, distributionData.away.disAll.split(',')) }">
                  {{ item }}
                </span>
              </div>
              <div class="period-item">
                <span class="label">主</span>
                <span class="value" v-for="(item, index) in distributionData.away.disHome.split(',')" :key="index"
                  :class="{ red: isMaxValue(item, distributionData.away.disHome.split(',')) }">
                  {{ item }}
                </span>
              </div>
              <div class="period-item">
                <span class="label">客</span>
                <span class="value" v-for="(item, index) in distributionData.away.disAway.split(',')" :key="index"
                  :class="{ red: isMaxValue(item, distributionData.away.disAway.split(',')) }">
                  {{ item }}
                </span>
              </div>
            </div>
            <noneBox v-else text="暂无数据" :isIocn="false"></noneBox>
          </div>
        </div>
      </div>
    </div>

    <!-- 积分排名 -->
    <div class="data-section" v-if="tableData.length > 0">
      <div class="section-header">
        <div class="red-line"></div>
        <span class="section-title">积分排名</span>
      </div>
      <div class="ranking-section">
        <div class="ranking-table">
          <div class="ranking-header">
            <img class="team-logo" :src="liveDetail.homeLogo" />
            <span class="team-name">{{ liveDetail.homeName }}</span>
          </div>
          <div class="table-header">
            <span>{{ liveDetail.sclassName }}</span>
            <span>赛</span>
            <span>胜平负</span>
            <span>进/失</span>
            <span>积分</span>
            <span class="red">排名</span>
          </div>
          <div class="table-row">
            <span class="rank">总</span>
            <span>{{ tableData[0].total }}</span>
            <span>{{ tableData[0].win }}/{{ tableData[0].draw }}/{{ tableData[0].loss }}</span>
            <span>{{ tableData[0].goals }}/{{ tableData[0].goalsAgainst }}</span>
            <span>{{ tableData[0].points }}</span>
            <span class="red">{{ tableData[0].position }}</span>
          </div>
          <div class="table-row">
            <span class="rank">主</span>
            <span>{{ tableData[0].homeTotal }}</span>
            <span>{{ tableData[0].homeWin }}/{{ tableData[0].homeDraw }}/{{ tableData[0].homeLoss }}</span>
            <span>{{ tableData[0].homeGoals }}/{{ tableData[0].homeGoalsAgainst }}</span>
            <span>{{ tableData[0].homePoints }}</span>
            <span class="red">{{ tableData[0].homePosition }}</span>
          </div>
          <div class="table-row">
            <span class="rank">客</span>
            <span>{{ tableData[0].awayTotal }}</span>
            <span>{{ tableData[0].awayWin }}/{{ tableData[0].awayDraw }}/{{ tableData[0].awayLoss }}</span>
            <span>{{ tableData[0].awayGoals }}/{{ tableData[0].awayGoalsAgainst }}</span>
            <span>{{ tableData[0].awayPoints }}</span>
            <span class="red">{{ tableData[0].awayPosition }}</span>
          </div>
        </div>
        <div class="ranking-table">
          <div class="ranking-header">
            <img class="team-logo" :src="liveDetail.awayLogo" />
            <span class="team-name">{{ liveDetail.awayName }}</span>
          </div>
          <div class="table-header">
            <span>{{ liveDetail.sclassName }}</span>
            <span>赛</span>
            <span>胜平负</span>
            <span>进/失</span>
            <span>积分</span>
            <span class="red">排名</span>
          </div>
          <div class="table-row">
            <span class="rank">总</span>
            <span>{{ tableData[1].total }}</span>
            <span>{{ tableData[1].win }}/{{ tableData[1].draw }}/{{ tableData[1].loss }}</span>
            <span>{{ tableData[1].goals }}/{{ tableData[1].goalsAgainst }}</span>
            <span>{{ tableData[1].points }}</span>
            <span class="red">{{ tableData[1].position }}</span>
          </div>
          <div class="table-row">
            <span class="rank">主</span>
            <span>{{ tableData[1].homeTotal }}</span>
            <span>{{ tableData[1].homeWin }}/{{ tableData[1].homeDraw }}/{{ tableData[1].homeLoss }}</span>
            <span>{{ tableData[1].homeGoals }}/{{ tableData[1].homeGoalsAgainst }}</span>
            <span>{{ tableData[1].homePoints }}</span>
            <span class="red">{{ tableData[1].homePosition }}</span>
          </div>
          <div class="table-row">
            <span class="rank">客</span>
            <span>{{ tableData[1].awayTotal }}</span>
            <span>{{ tableData[1].awayWin }}/{{ tableData[1].awayDraw }}/{{ tableData[1].awayLoss }}</span>
            <span>{{ tableData[1].awayGoals }}/{{ tableData[1].awayGoalsAgainst }}</span>
            <span>{{ tableData[1].awayPoints }}</span>
            <span class="red">{{ tableData[1].awayPosition }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 历史交锋 -->
    <div class="data-section" v-if="historyStats?.totalMatches > 0">
      <div class="section-header">
        <div class="red-line"></div>
        <span class="section-title">历史交锋</span>
      </div>
      <div class="history-section">
        <div class="history-header">
          <div class="top">
            <div class="logo">
              <img class="team-logo" :src="liveDetail.homeLogo" />
              <span class="team-name">{{ liveDetail.homeName }}</span>
            </div>
            <div class="switch-buttons">
              <div class="switch-btn" :class="{ active: historyMatchCount === 10 }" @click="switchHistoryCount(10)">10场
              </div>
              <div class="switch-btn" :class="{ active: historyMatchCount === 20 }" @click="switchHistoryCount(20)">20场
              </div>
            </div>
          </div>
          <div class="bottom">
            <div class="stats">
              <span class="stat-item first">
                <p class="label">近</p>
                <p>{{ historyStats.totalMatches }}场</p>
              </span>
              <span class="stat-item">
                <p class="label">进失<span class="number">{{ historyStats.goalsFor + historyStats.goalsAgainst }}</span></p>
                <p>进{{ historyStats.goalsFor }}失{{ historyStats.goalsAgainst }}</p>
              </span>
              <span class="stat-item">
                <p class="label">胜率<span class="number">{{ historyStats.winRate }}</span></p>
                <p>{{ historyStats.wins }}胜{{ historyStats.draws }}平{{ historyStats.losses }}负</p>
              </span>
              <span class="stat-item">
                <p class="label">赢率<span class="number">{{ historyStats.handicapWinRate }}</span></p>
                <p>{{ historyStats.winHandicap }}赢{{ historyStats.drawHandicap }}走{{ historyStats.loseHandicap }}输</p>
              </span>
              <span class="stat-item">
                <p class="label">大率<span class="number">{{ historyStats.bigBallRate }}</span></p>
                <p>{{ historyStats.bigBall }}大{{ historyStats.drawBall }}走{{ historyStats.smallBall }}小</p>
              </span>
            </div>
          </div>
        </div>
        <div class="history-matches">
          <div class="match-item">
            <span class="date">日期/赛事</span>
            <span class="home">主队</span>
            <span class="score">比分</span>
            <span class="away">客队</span>
            <span class="half">让球</span>
            <span class="size">大小</span>
            <span class="corner">角球</span>
          </div>
          <div class="match-item" v-for="item in switchHistoryData" :key="item.id">
            <span class="date">{{ dayjs(item.matchTime).format('YYYY-MM-DD') }}<br />{{ item.aliasName }}</span>
            <span class="home">{{ item.homeName }}</span>
            <span class="score">
              <p :class="[getScoreClass(item)]">{{ item.homeScore }}-{{ item.awayScore }}</p>
              ({{ item.homeHalfScore }}-{{ item.awayHalfScore }})
            </span>
            <span class="away">{{ item.awayName }}</span>
            <span class="half"
              :class="[{ red: item.asianStatus === '2' }, { green: item.asianStatus === '1' }, { blue: item.asianStatus === '3' }]">{{
                item.asianPlate }}<br />{{ getAsianStatusName(item.asianStatus) }}</span>
            <span class="corner"
              :class="[{ red: item.sizeStatus === '2' }, { green: item.sizeStatus === '1' }, { blue: item.sizeStatus === '3' }]">{{
                item.sizePlate }}<br />{{
                getSizePlateName(item.sizeStatus) }}</span>
            <span class="corner">{{ (item.homeCornerKick ? item.homeCornerKick : 0) + (item.awayCornerKick ?
              item.awayCornerKick : 0) }}<br />{{ item.homeCornerKick }}-{{
                item.awayCornerKick }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 近期战绩 -->
    <div class="data-section" v-if="awaySwitchHistoryData.length > 0 || homeSwitchHistoryData.length > 0">
      <div class="section-header">
        <div class="red-line"></div>
        <span class="section-title">近期战绩</span>
      </div>
      <div class="recent-records">
        <div class="record-table">
          <div class="history-header">
            <div class="top">
              <div class="logo">
                <img class="team-logo" :src="liveDetail.homeLogo" />
                <span class="team-name">{{ liveDetail.homeName }}</span>
              </div>
              <div class="switch-buttons">
                <div class="switch-btn" :class="{ active: homeMatchCount === 10 }" @click="switchHomeCount(10)">
                  10场
                </div>
                <div class="switch-btn" :class="{ active: homeMatchCount === 20 }" @click="switchHomeCount(20)">
                  20场
                </div>
              </div>
            </div>
            <div class="bottom">
              <div class="stats">
                <span class="stat-item first">
                  <p class="label">近</p>
                  <p>{{ homeStats.totalMatches }}场</p>
                </span>
                <span class="stat-item">
                  <p class="label">进失<span class="number">{{ homeStats.goalsFor + homeStats.goalsAgainst }}</span></p>
                  <p>进{{ homeStats.goalsFor }}失{{ homeStats.goalsAgainst }}</p>
                </span>
                <span class="stat-item">
                  <p class="label">胜率<span class="number">{{ homeStats.winRate }}</span></p>
                  <p>{{ homeStats.wins }}胜{{ homeStats.draws }}平{{ homeStats.losses }}负</p>
                </span>
                <span class="stat-item">
                  <p class="label">赢率<span class="number">{{ homeStats.handicapWinRate }}</span></p>
                  <p>{{ homeStats.winHandicap }}赢{{ homeStats.drawHandicap }}走{{ homeStats.loseHandicap }}输</p>
                </span>
                <span class="stat-item">
                  <p class="label">大率<span class="number">{{ homeStats.bigBallRate }}</span></p>
                  <p>{{ homeStats.bigBall }}大{{ homeStats.drawBall }}走{{ homeStats.smallBall }}小</p>
                </span>
              </div>
            </div>
          </div>
          <div class="record-matches" v-if="homeSwitchHistoryData.length > 0">
            <div class="match-header">
              <span>日期/赛事</span>
              <span>主队</span>
              <span>比分</span>
              <span>客队</span>
              <span>让球</span>
              <span>大小</span>
              <span>角球</span>
            </div>
            <div class="match-row" v-for="item in homeSwitchHistoryData" :key="item.id">
              <span class="date">{{ dayjs(item.matchTime).format('YYYY-MM-DD') }}<br />{{ item.aliasName }}</span>
              <span class="home">{{ item.homeName }}</span>
              <span class="score">
                <p :class="[getScoreClassByTeam(item, liveDetail.homeName)]">{{ item.homeScore }}-{{ item.awayScore }}
                </p>
                ({{ item.homeHalfScore }}-{{ item.awayHalfScore }})
              </span>
              <span class="away">{{ item.awayName }}</span>
              <span class="handicap"
                :class="[{ red: item.asianStatus === '2' }, { green: item.asianStatus === '1' }, { blue: item.sizeStatus === '3' }]">{{
                  item.asianPlate }}<br />{{ getAsianStatusName(item.asianStatus) }}</span>
              <span class="total"
                :class="[{ red: item.sizeStatus === '2' }, { green: item.sizeStatus === '1' }, { blue: item.sizeStatus === '3' }]">
                {{ item.sizePlate }}<br />
                {{ getSizePlateName(item.sizeStatus) }}</span>
              <span class="corner">{{ (item.homeCornerKick ? item.homeCornerKick : 0) + (item.awayCornerKick ?
                item.awayCornerKick : 0) }}<br />{{ item.homeCornerKick }}-{{
                  item.awayCornerKick }}</span>
            </div>
          </div>
          <noneBox v-else text="暂无数据" :isIocn="false"></noneBox>
        </div>
        <div class="record-table">
          <div class="history-header">
            <div class="top">
              <div class="logo">
                <img class="team-logo" :src="liveDetail.awayLogo" />
                <span class="team-name">{{ liveDetail.awayName }}</span>
              </div>
              <div class="switch-buttons">
                <div class="switch-btn" :class="{ active: awayMatchCount === 10 }" @click="switchAwayCount(10)">
                  10场
                </div>
                <div class="switch-btn" :class="{ active: awayMatchCount === 20 }" @click="switchAwayCount(20)">
                  20场
                </div>
              </div>
            </div>
            <div class="bottom">
              <div class="stats">
                <span class="stat-item first">
                  <p class="label">近</p>
                  <p>{{ awayStats.totalMatches }}场</p>
                </span>
                <span class="stat-item">
                  <p class="label">进失<span class="number">{{ awayStats.goalsFor + awayStats.goalsAgainst }}</span></p>
                  <p>进{{ awayStats.goalsFor }}失{{ awayStats.goalsAgainst }}</p>
                </span>
                <span class="stat-item">
                  <p class="label">胜率<span class="number">{{ awayStats.winRate }}</span></p>
                  <p>{{ awayStats.wins }}胜{{ awayStats.draws }}平{{ awayStats.losses }}负</p>
                </span>
                <span class="stat-item">
                  <p class="label">赢率<span class="number">{{ awayStats.handicapWinRate }}</span></p>
                  <p>{{ awayStats.winHandicap }}赢{{ awayStats.drawHandicap }}走{{ awayStats.loseHandicap }}输</p>
                </span>
                <span class="stat-item">
                  <p class="label">大率<span class="number">{{ awayStats.bigBallRate }}</span></p>
                  <p>{{ awayStats.bigBall }}大{{ awayStats.drawBall }}走{{ awayStats.smallBall }}小</p>
                </span>
              </div>
            </div>
          </div>
          <div class="record-matches" v-if="awaySwitchHistoryData.length > 0">
            <div class="match-header">
              <span>日期/赛事</span>
              <span>主队</span>
              <span>比分</span>
              <span>客队</span>
              <span>让球</span>
              <span>大小</span>
              <span>角球</span>
            </div>
            <div class="match-row" v-for="item in awaySwitchHistoryData" :key="item.id">
              <span class="date">{{ dayjs(item.matchTime).format('YYYY-MM-DD') }}<br />{{ item.aliasName }}</span>
              <span class="home">{{ item.homeName }}</span>
              <span class="score">
                <p :class="[getScoreClassByTeam(item, liveDetail.awayName)]">{{ item.homeScore }}-{{ item.awayScore }}
                </p>
                ({{ item.homeHalfScore }}-{{ item.awayHalfScore }})
              </span>
              <span class="away">{{ item.awayName }}</span>
              <span class="handicap"
                :class="[{ red: item.asianStatus === '2' }, { green: item.asianStatus === '1' }, { blue: item.sizeStatus === '3' }]">{{
                  item.asianPlate }}<br />{{ getAsianStatusName(item.asianStatus) }}</span>
              <span class="total"
                :class="[{ red: item.sizeStatus === '2' }, { green: item.sizeStatus === '1' }, { blue: item.sizeStatus === '3' }]">
                {{ item.sizePlate }}<br />{{ getSizePlateName(item.sizeStatus) }}</span>
              <span class="corner">{{ (item.homeCornerKick ? item.homeCornerKick : 0) + (item.awayCornerKick ?
                item.awayCornerKick : 0) }}<br />{{ item.homeCornerKick }}-{{
                  item.awayCornerKick }}</span>
            </div>
          </div>
          <noneBox v-else text="暂无数据" :isIocn="false"></noneBox>
        </div>
      </div>
    </div>
    <!-- 伤停情况 -->
    <div class="data-section" v-if="injuryList.length > 0">
      <div class="section-header">
        <div class="red-line"></div>
        <span class="section-title">伤停情况</span>
      </div>
      <div class="injury-section">
        <div class="injury-table">
          <div class="injury-header">
            <img class="team-logo" :src="liveDetail.homeLogo" />
            <span class="team-name">{{ liveDetail.homeName }}</span>
          </div>
          <div class="injury-list">
            <div class="injury-header-row">
              <span>球员</span>
              <span>位置</span>
              <span>原因</span>
            </div>
            <template v-for="item in injuryList" :key="item.id">
              <div class="injury-row" v-if="item && item.type === 1">
                <div class="player-info">
                  <img class="player-avatar" :src="item.playerLogo || defaultPlayerLogo" />
                  <span class="player-name">{{ item.playerName }}</span>
                </div>
                <span class="position">{{ getPositionName(item.position) }}</span>
                <span class="reason">{{ item.reason }}</span>
              </div>
            </template>
            <div class="data-none" v-if="injuryList.findIndex(item => item.type === 1) === -1">
              无伤病信息
            </div>
          </div>
        </div>
        <div class="injury-table">
          <div class="injury-header">
            <img class="team-logo" :src="liveDetail.awayLogo" />
            <span class="team-name">{{ liveDetail.awayName }}</span>
          </div>
          <div class="injury-list">
            <div class="injury-header-row">
              <span>球员</span>
              <span>位置</span>
              <span>原因</span>
            </div>
            <template v-for="item in injuryList" :key="item.id">
              <div class="injury-row" v-if="item && item.type === 2">
                <div class="player-info">
                  <img class="player-avatar" :src="item.playerLogo || defaultPlayerLogo" />
                  <span class="player-name">{{ item.playerName }}</span>
                </div>
                <span class="position">{{ getPositionName(item.position) }}</span>
                <span class="reason">{{ item.reason }}</span>
              </div>
            </template>
            <div class="data-none" v-if="injuryList.findIndex(item => item.type === 2) === -1">
              无伤病信息
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 半全场胜负 -->
    <div class="data-section" v-if="halfHeaderData.home_allNum != 0">
      <div class="section-header">
        <div class="sub-header">
          <div class="red-line"></div>
          <span class="section-title">半全场胜负</span>
        </div>
        <div class="switch-buttons">
          <div class="switch-btn" :class="{ active: halfAllMatchCount === 10 }" @click="switchHalfCount(10)">10场
          </div>
          <div class="switch-btn" :class="{ active: halfAllMatchCount === 20 }" @click="switchHalfCount(20)">20场
          </div>
        </div>
      </div>
      <div class="half-all-section">
        <div class="team-header">
          <div>
            <img class="team-logo" :src="liveDetail.homeLogo" />
            <span class="team-name">{{ liveDetail.homeName }}</span>
          </div>
          <div>
            <img class="team-logo" :src="liveDetail.awayLogo" />
            <span class="team-name">{{ liveDetail.awayName }}</span>
          </div>
        </div>
        <div class="half-all-table">
          <div class="table-header">
            <span>主({{ halfHeaderData.home_homeNum }})</span>
            <span>客({{ halfHeaderData.home_awayNum }})</span>
            <span>总({{ halfHeaderData.home_allNum }}) </span>
            <span>全场</span>
            <span>主({{ halfHeaderData.away_homeNum }})</span>
            <span>客({{ halfHeaderData.away_awayNum }})</span>
            <span>总({{ halfHeaderData.away_allNum }})</span>
          </div>
          <div class="table-body">
            <div class="table-row" v-for="(item, index) in halfAllStats" :key="index">
              <span :class="{ red: isMaxNumber(item.home.homeNum, halfAllStats, 'home.homeNum') }">{{ item.home.homeNum
              }}</span>
              <span :class="{ red: isMaxNumber(item.home.awayNum, halfAllStats, 'home.awayNum') }">{{ item.home.awayNum
              }}</span>
              <span :class="{ red: isMaxNumber(item.home.allNum, halfAllStats, 'home.allNum') }">{{ item.home.allNum
              }}</span>
              <span>{{ item.gapText }}</span>
              <span :class="{ red: isMaxNumber(item.away.homeNum, halfAllStats, 'away.homeNum') }">{{ item.away.homeNum
              }}</span>
              <span :class="{ red: isMaxNumber(item.away.awayNum, halfAllStats, 'away.awayNum') }">{{ item.away.awayNum
              }}</span>
              <span :class="{ red: isMaxNumber(item.away.allNum, halfAllStats, 'away.allNum') }">{{ item.away.allNum
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <noneBox
    v-if="!distributionData.home.disAll && tableData.length == 0 && historyStats?.totalMatches == 0 && injuryList.length == 0 && halfHeaderData.home_allNum == 0"
    text="暂无数据"></noneBox>
</template>

<script setup lang="ts">
// 组件逻辑
import { ref, reactive, computed } from 'vue'
import dayjs from 'dayjs'
import { isMaxNumber } from '@/utils/common'
import defaultPlayerLogo from '@/static/index/default-player-logo.png'
import { getDistributionApi, getTableApi, getHistoryApi, getInjuryApi } from '@/api/match'

// 判断当前值是否为数组中的最大值
const isMaxValue = (currentValue: string, dataArray: string[]): boolean => {
  const values = dataArray.map(item => parseInt(item.trim()) || 0)
  const maxValue = Math.max(...values)
  const currentNum = parseInt(currentValue.trim()) || 0
  return currentNum === maxValue && maxValue > 0
}


const props = defineProps({
  matchId: {
    type: Number,
    required: true
  },
  liveDetail: {
    type: Object,
    required: true,
    default: () => ({
      awayLogo: '',
      awayName: '',
      homeLogo: '',
      homeName: '',
      sclassName: ''
    })
  }
})
//进球分布
const distributionData = reactive({
  home: {
    disHome: '',
    disAll: '',
    disAway: ''
  },
  away: {
    disHome: '',
    disAll: '',
    disAway: ''
  }
})
const getDistribution = async () => {
  let res = await getDistributionApi({ matchId: props.matchId })
  if (res.data.length > 0) {
    let home = res.data.find((item) => {
      return item.type === 1;
    })
    distributionData.home = Object.assign(distributionData.home, home)
    let away = res.data.find((item) => {
      return item.type === 2;
    })
    distributionData.away = Object.assign(distributionData.away, away)
  }
}
//积分排名
interface MatchFootTableDetailRes {
  /**
   * 客队-平场次
   */
  awayDraw?: number;
  /**
   * 客队-净胜球
   */
  awayGoalDiff?: number;
  /**
   * 客队-进球
   */
  awayGoals?: number;
  /**
   * 客队-失球
   */
  awayGoalsAgainst?: number;
  /**
   * 客队-负场次
   */
  awayLoss?: number;
  /**
   * 客队-积分
   */
  awayPoints?: number;
  /**
   * 客队-排名
   */
  awayPosition?: number;
  /**
   * 客队-场次
   */
  awayTotal?: number;
  /**
   * 客队-胜场次
   */
  awayWin?: number;
  /**
   * 平场数
   */
  draw?: number;
  /**
   * 净胜球
   */
  goalDiff?: number;
  /**
   * 进球
   */
  goals?: number;
  /**
   * 失球
   */
  goalsAgainst?: number;
  /**
   * 主队-平场次
   */
  homeDraw?: number;
  /**
   * 主队-净胜球
   */
  homeGoalDiff?: number;
  /**
   * 主队-进球
   */
  homeGoals?: number;
  /**
   * 主队-失球
   */
  homeGoalsAgainst?: number;
  /**
   * 主队-负场次
   */
  homeLoss?: number;
  /**
   * 主队-积分
   */
  homePoints?: number;
  /**
   * 主队-排名
   */
  homePosition?: number;
  /**
   * 主队-场次
   */
  homeTotal?: number;
  /**
   * 主队-胜场次
   */
  homeWin?: number;
  id?: number;
  /**
   * 负场数
   */
  loss?: number;
  /**
   * 比赛id
   */
  matchId?: number;
  /**
   * 积分
   */
  points?: number;
  /**
   * 排名
   */
  position?: number;
  /**
   * 球队id
   */
  teamId?: number;
  /**
   * 比赛场次
   */
  total?: number;
  /**
   * 胜场数
   */
  win?: number;
  [property: string]: any;
}
const tableData = ref<MatchFootTableDetailRes[]>([])
const getTable = async () => {
  let res = await getTableApi({ matchId: props.matchId })
  tableData.value = res.data
}

// 示例：如何在积分排名中使用最大值判断
// 假设有一组积分数据
const sampleScores = [15, 8, 7, 12, 10]
// 使用方法：isMaxNumber(currentScore, sampleScores) 来判断是否为最大值

/**
 * 使用说明：
 *
 * 1. 对于字符串数组（如进球分布数据）：
 *    :class="{ red: isMaxValue(item, dataArray) }"
 *
 * 2. 对于数字数组（如积分排名数据）：
 *    :class="{ red: isMaxNumber(currentValue, numberArray) }"
 *
 * 3. 示例用法：
 *    <span :class="{ red: isMaxNumber(15, [15, 8, 7]) }">15</span>
 *    这会为数值15添加red类，因为它是数组中的最大值
 */

// 历史交锋和近期战绩的场次切换
const historyMatchCount = ref(10) // 10场或20场


// 切换历史交锋场次
const switchHistoryCount = (count: number) => {
  historyMatchCount.value = count
  switchHistoryData.value = historyData.value.slice(0, count);
}
const switchHomeCount = (count: number) => {
  homeMatchCount.value = count
  homeSwitchHistoryData.value = homeHistoryData.value.slice(0, count);
}
const switchAwayCount = (count: number) => {
  awayMatchCount.value = count
  awaySwitchHistoryData.value = awayHistoryData.value.slice(0, count);
}

// 计算历史交锋统计数据

const calculateHistoryStats = (teamName: string, historyData: any[]) => {
  if (!teamName || !historyData || historyData.length === 0) {
    return {
      wins: 0,
      draws: 0,
      losses: 0,
      winRate: '0%',
      drawRate: '0%',
      lossRate: '0%',
      totalMatches: 0,
      goalsFor: 0,
      goalsAgainst: 0,
      winHandicap: 0,
      loseHandicap: 0,
      drawHandicap: 0,
      handicapWinRate: '0%',
      bigBall: 0,
      smallBall: 0,
      drawBall: 0,
      bigBallRate: '0%'
    }
  }

  let wins = 0
  let draws = 0
  let losses = 0
  let goalsFor = 0
  let goalsAgainst = 0
  let winHandicap = 0
  let loseHandicap = 0
  let drawHandicap = 0
  let bigBall = 0
  let smallBall = 0
  let drawBall = 0

  historyData.forEach(match => {
    const isHome = match.homeName === teamName
    const isAway = match.awayName === teamName

    if (isHome || isAway) {
      const homeScore = parseInt(match.homeScore) || 0
      const awayScore = parseInt(match.awayScore) || 0

      // 计算进球失球
      if (isHome) {
        goalsFor += homeScore
        goalsAgainst += awayScore
      } else {
        goalsFor += awayScore
        goalsAgainst += homeScore
      }

      // 计算胜负平（基于比分）
      if (homeScore === awayScore) {
        draws++
      } else if ((isHome && homeScore > awayScore) || (isAway && awayScore > homeScore)) {
        wins++
      } else {
        losses++
      }
    }

    // 计算赢率（基于 asianStatus，不区分主客队）
    // asianStatus: 1-输, 2-赢, 3-平
    const asianStatus = parseInt(match.asianStatus) || 0
    if (asianStatus === 2) {
      winHandicap++
    } else if (asianStatus === 1) {
      loseHandicap++
    } else if (asianStatus === 3) {
      drawHandicap++
    }

    // 计算大率（基于 sizeStatus，不区分主客队）
    // sizeStatus: 1-小, 2-大, 3-平
    const sizeStatus = parseInt(match.sizeStatus) || 0
    if (sizeStatus === 2) {
      bigBall++
    } else if (sizeStatus === 1) {
      smallBall++
    } else if (sizeStatus === 3) {
      drawBall++
    }
  })

  const totalMatches = wins + draws + losses
  const winRate = totalMatches > 0 ? Math.round((wins / totalMatches) * 100) : 0
  const drawRate = totalMatches > 0 ? Math.round((draws / totalMatches) * 100) : 0
  const lossRate = totalMatches > 0 ? Math.round((losses / totalMatches) * 100) : 0

  // 计算赢率（让球盘口）
  const totalHandicapMatches = winHandicap + loseHandicap + drawHandicap
  const handicapWinRate = totalHandicapMatches > 0 ? Math.round((winHandicap / totalMatches) * 100) : 0

  // 计算大率（大小球盘口）
  const totalSizeMatches = bigBall + smallBall + drawBall
  const bigBallRate = totalSizeMatches > 0 ? Math.round((bigBall / totalMatches) * 100) : 0

  return {
    wins,
    draws,
    losses,
    winRate: `${winRate}%`,
    drawRate: `${drawRate}%`,
    lossRate: `${lossRate}%`,
    totalMatches,
    goalsFor,
    goalsAgainst,
    winHandicap,
    loseHandicap,
    drawHandicap,
    handicapWinRate: `${handicapWinRate}%`,
    bigBall,
    smallBall,
    drawBall,
    bigBallRate: `${bigBallRate}%`
  }
}

// 计算历史交锋统计数据（响应式）
const historyStats = computed(() => {
  return calculateHistoryStats(props.liveDetail.homeName, switchHistoryData.value)
})

// 计算近期战绩统计数据（响应式）
const homeStats = computed(() => {
  return calculateHistoryStats(props.liveDetail.homeName, homeSwitchHistoryData.value)
})
const awayStats = computed(() => {
  return calculateHistoryStats(props.liveDetail.awayName, awaySwitchHistoryData.value)
})
//历史交锋
interface MatchFootHistoryDetailRes {
  /**
   * 联赛名称
   */
  aliasName?: string;
  /**
   * 让分
   */
  asianPlate?: string;
  /**
   * 让分走势 1.输 2.赢 3.平
   */
  asianStatus?: string;
  /**
   * 客队角球
   */
  awayCornerKick?: number;
  /**
   * 客队半场
   */
  awayHalfScore?: number;
  /**
   * 客队名称
   */
  awayName?: string;
  /**
   * 客队加时比分
   */
  awayOverScore?: number;
  /**
   * 客队比分
   */
  awayScore?: number;
  /**
   * 客队id
   */
  awayTeamId?: number;
  /**
   * 主队角球
   */
  homeCornerKick?: number;
  /**
   * 主队半场
   */
  homeHalfScore?: number;
  /**
   * 主队名称
   */
  homeName?: string;
  /**
   * 主队加时比分
   */
  homeOverScore?: number;
  /**
   * 主队比分
   */
  homeScore?: number;
  /**
   * 主队id
   */
  homeTeamId?: number;
  /**
   * 比赛id
   */
  id?: number;
  /**
   * 比赛状态
   */
  matchState?: number;
  /**
   * 比赛时间
   */
  matchTime?: string;
  /**
   * 总分
   */
  sizePlate?: string;
  /**
   * 总分走势 1.小 2.大 3.平
   */
  sizeStatus?: string;
  [property: string]: any;
}
const historyData = ref<MatchFootHistoryDetailRes[]>([])
const switchHistoryData = ref<MatchFootHistoryDetailRes[]>([])
const getHistory = async () => {
  //0.历史交锋 1.主队 2.客队
  let res = await getHistoryApi({ matchId: props.matchId, type: 0 })
  historyData.value = res.data;
  switchHistoryData.value = res.data.slice(0, historyMatchCount.value);
}
//获取主队近期战绩
const homeHistoryData = ref<MatchFootHistoryDetailRes[]>([])
const homeSwitchHistoryData = ref<MatchFootHistoryDetailRes[]>([])
const homeMatchCount = ref(10) // 10场或20场
const getHomeHistory = async () => {
  //0.历史交锋 1.主队 2.客队
  let res = await getHistoryApi({ matchId: props.matchId, type: 1 })
  homeHistoryData.value = res.data;
  homeSwitchHistoryData.value = res.data.slice(0, homeMatchCount.value);
  homeHalfSwitchData.value = res.data.slice(0, halfAllMatchCount.value);
}
//获取客队近期战绩
const awayHistoryData = ref<MatchFootHistoryDetailRes[]>([])
const awaySwitchHistoryData = ref<MatchFootHistoryDetailRes[]>([])
const awayMatchCount = ref(10) // 10场或20场
const getAwayHistory = async () => {
  //0.历史交锋 1.主队 2.客队
  let res = await getHistoryApi({ matchId: props.matchId, type: 2 })
  awayHistoryData.value = res.data;
  awaySwitchHistoryData.value = res.data.slice(0, awayMatchCount.value);
  awayHalfSwitchData.value = res.data.slice(0, halfAllMatchCount.value);
}
//获取伤停
interface MatchFootInjuryDetailRes {
  id?: number;
  /**
   * 比赛id
   */
  matchId?: number;
  /**
   * 球员logo
   */
  playerLogo?: string;
  /**
   * 球员名称
   */
  playerName?: string;
  /**
   * 球员位置，F-前锋、M-中场、D-后卫、G-守门员、其他为未知
   */
  position?: string;
  /**
   * 伤停原因
   */
  reason?: string;
  /**
   * 球员号
   */
  shirtNumber?: string;
  /**
   * 球队id
   */
  teamId?: number;
  /**
   * 1主 2客
   */
  type?: number;
  [property: string]: any;
}
let injuryList = ref<MatchFootInjuryDetailRes[]>([])
const getInjury = async () => {
  let res = await getInjuryApi({ matchId: props.matchId })
  injuryList.value = res.data
}
const positionMap = {
  F: '前锋',
  M: '中场',
  D: '后卫',
  G: '守门员',
};
const getPositionName = (position) => {
  return positionMap[position] || '未知';
};
const sizeStatusMap = {
  1: '小',
  2: '大',
  3: '走'
};
const getSizePlateName = (sizeStatus) => {
  return sizeStatusMap[sizeStatus] || '-';
};
const asianStatusMap = {
  1: '输',
  2: '赢',
  3: '走'
}
const getAsianStatusName = (asianStatus) => {
  return asianStatusMap[asianStatus] || '-';
};

// 判断比分是否显示红色
const getScoreClass = (match) => {
  const homeScore = parseInt(match.homeScore) || 0
  const awayScore = parseInt(match.awayScore) || 0

  // 如果当前比赛的主队是 liveDetail.homeName，则主队比分大时显示红色
  if (match.homeName === props.liveDetail.homeName) {
    if (homeScore > awayScore) {
      return 'red'
    } else if (awayScore > homeScore) {
      return 'green'
    } else {
      return 'blue'
    }
  }
  // 如果当前比赛的客队是 liveDetail.homeName，则客队比分大时显示红色
  else if (match.awayName === props.liveDetail.homeName) {
    // return awayScore > homeScore
    if (awayScore > homeScore) {
      return 'red'
    } else if (homeScore > awayScore) {
      return 'green'
    } else {
      return 'blue'
    }
  }

  return false
};
const getScoreClassByTeam = (match, teamName) => {
  const homeScore = parseInt(match.homeScore) || 0
  const awayScore = parseInt(match.awayScore) || 0

  // 如果当前比赛的主队是 liveDetail.homeName，则主队比分大时显示红色
  if (match.homeName === teamName) {
    if (homeScore > awayScore) {
      return 'red'
    } else if (awayScore > homeScore) {
      return 'green'
    } else {
      return 'blue'
    }
  }
  // 如果当前比赛的客队是 liveDetail.homeName，则客队比分大时显示红色
  else if (match.awayName === teamName) {
    // return awayScore > homeScore
    if (awayScore > homeScore) {
      return 'red'
    } else if (homeScore > awayScore) {
      return 'green'
    } else {
      return 'blue'
    }
  }

  return false
};
//获取半全场胜负
const halfAllMatchCount = ref(10) // 10场或20场
const awayHalfSwitchData = ref<MatchFootHistoryDetailRes[]>([])
const homeHalfSwitchData = ref<MatchFootHistoryDetailRes[]>([])
// const halfHeaderData = reactive({
//   home_homeNum: 0,
//   home_awayNum: 0,
//   home_allNum: 0,
//   away_homeNum: 0,
//   away_awayNum: 0,
//   away_allNum: 0
// })
const halfHeaderData = computed(() => {
  const data = halfAllStats.value
  return {
    home_homeNum: data.reduce((acc, cur) => acc + cur.home.homeNum, 0),
    home_awayNum: data.reduce((acc, cur) => acc + cur.home.awayNum, 0),
    home_allNum: data.reduce((acc, cur) => acc + cur.home.allNum, 0),
    away_homeNum: data.reduce((acc, cur) => acc + cur.away.homeNum, 0),
    away_awayNum: data.reduce((acc, cur) => acc + cur.away.awayNum, 0),
    away_allNum: data.reduce((acc, cur) => acc + cur.away.allNum, 0)
  }
})
const switchHalfCount = (count: number) => {
  halfAllMatchCount.value = count
  awayHalfSwitchData.value = awayHistoryData.value.slice(0, count);
  homeHalfSwitchData.value = homeHistoryData.value.slice(0, count);
}
// 计算近期战绩统计数据（响应式）
const halfAllStats = computed(() => {
  return calcHalfAllStats(homeHalfSwitchData.value, awayHalfSwitchData.value)
})

const calcHalfAllStats = (homeList, awayList) => {
  let dataList = [
    {
      home: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      },
      gapText: '胜胜',
      away: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      }
    },
    {
      home: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      },
      gapText: '胜平',
      away: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      }
    },
    {
      home: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      },
      gapText: '胜负',
      away: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      }
    }, {
      home: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      },
      gapText: '平胜',
      away: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      }
    },
    {
      home: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      },
      gapText: '平平',
      away: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      }
    },
    {
      home: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      },
      gapText: '平负',
      away: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      }
    }, {
      home: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      },
      gapText: '负胜',
      away: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      }
    },
    {
      home: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      },
      gapText: '负平',
      away: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      }
    },
    {
      home: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      },
      gapText: '负负',
      away: {
        homeNum: 0,
        awayNum: 0,
        allNum: 0
      }
    }
  ]
  if (homeList.length > 0) {
    homeList.forEach((matchItem) => {
      let guestBqc = matchItem.homeBqc;
      let homeBqc = guestBqc[0].split(',')
      let awayBqc = guestBqc[1].split(',')
      if (guestBqc[0]) {
        homeBqc.forEach((subItem, subind) => {
          dataList[subind].home.homeNum += parseInt(subItem);
        });
        awayBqc.forEach((subItem, subind) => {
          dataList[subind].home.awayNum += parseInt(subItem);
          dataList[subind].home.allNum = dataList[subind].home.awayNum + dataList[subind].home.homeNum;
        });
      }
    })
  }
  if (awayList.length > 0) {
    awayList.forEach((matchItem) => {
      let guestBqc = matchItem.guestBqc;
      let homeBqc = guestBqc[0].split(',')
      let awayBqc = guestBqc[1].split(',')
      if (guestBqc[0]) {
        homeBqc.forEach((subItem, subind) => {
          dataList[subind].away.homeNum += parseInt(subItem);
        });
        awayBqc.forEach((subItem, subind) => {
          dataList[subind].away.awayNum += parseInt(subItem);
          dataList[subind].away.allNum = dataList[subind].away.awayNum + dataList[subind].away.homeNum;
        });
      }
    })
  }
  // halfHeaderData.home_homeNum = dataList.reduce((acc, cur) => acc + cur.home.homeNum, 0)
  // halfHeaderData.home_awayNum = dataList.reduce((acc, cur) => acc + cur.home.awayNum, 0)
  // halfHeaderData.home_allNum = dataList.reduce((acc, cur) => acc + cur.home.allNum, 0)
  // halfHeaderData.away_homeNum = dataList.reduce((acc, cur) => acc + cur.away.homeNum, 0)
  // halfHeaderData.away_awayNum = dataList.reduce((acc, cur) => acc + cur.away.awayNum, 0)
  // halfHeaderData.away_allNum = dataList.reduce((acc, cur) => acc + cur.away.allNum, 0)
  return dataList;
}
getDistribution()
getTable()
getHistory()
getHomeHistory()
getAwayHistory()
getInjury()
</script>

<style lang="scss" scoped>
.live-data-view {
  width: 100%;
  background: #fff;
  overflow-x: auto;

  .data-section {
    margin-bottom: 20px;
    padding: 16px;

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      // padding: 10px 6px;
      .sub-header {
        display: flex;
        align-items: center;
      }

      .red-line {
        width: 4px;
        height: 20px;
        background: #EB0000;
        margin-right: 10px;
      }

      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      .switch-buttons {
        display: flex;
        border-radius: 30px;
        background: #F5F5F5;
        margin-left: auto;

        .switch-btn {
          color: #666;
          font-size: 12px;
          padding: 4px 8px;
          border-radius: 30px;
          cursor: pointer;
          transition: all 0.2s;
          min-width: 40px;
          text-align: center;

          &:hover {
            background: #E8E8E8;
          }

          &.active {
            background: #EB0000;
            color: white;
            border-color: #EB0000;
          }
        }
      }
    }
  }

  // 进球分布样式
  .goal-distribution {
    display: flex;
    gap: 20px;

    .team-data {
      flex: 1;
      border-radius: 8px;

      .team-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        .team-logo {
          width: 24px;
          height: 24px;
          margin-right: 8px;
        }

        .team-name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }

      .time-periods {
        .period-layout {
          display: grid;
          grid-template-columns: 30px 1fr;
          background-color: #F4F4F4;
          gap: 10px;
          margin-bottom: 10px;
          color: #333;

          .label-column {
            width: 30px;
          }

          .period-header {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 10px;

            span {
              text-align: center;
              font-size: 12px;
              color: #666;
              padding: 5px;
            }
          }
        }

        .period-data {
          .period-item {
            display: grid;
            grid-template-columns: 30px repeat(6, 1fr);
            border-bottom: 1px solid #F4F4F4;
            gap: 10px;
            margin-bottom: 8px;
            align-items: center;

            .label {
              font-size: 14px;
              color: #333;
              font-weight: 500;
            }

            .value {
              text-align: center;
              font-size: 14px;
              color: #333;
              padding: 4px;

              &.red {
                color: #EB0000;
                font-weight: 600;
              }
            }
          }
        }
      }
    }
  }

  // 积分排名样式
  .ranking-section {
    display: flex;
    gap: 20px;

    .ranking-table {
      flex: 1;
      // border: 1px solid #E8E8E8;
      border-radius: 8px;
      padding: 15px;

      .ranking-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        .team-logo {
          width: 24px;
          height: 24px;
          margin-right: 8px;
        }

        .team-name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }

      .table-header {
        display: grid;
        grid-template-columns: 1fr 0.8fr 1.2fr 1fr 0.8fr 0.8fr;
        gap: 8px;
        margin-bottom: 10px;
        background: #F5F5F5;

        span {
          text-align: center;
          font-size: 12px;
          color: #666;
          padding: 5px;

          &.red {
            color: #EB0000;
            font-weight: 600;
          }
        }
      }

      .table-row {
        display: grid;
        grid-template-columns: 1fr 0.8fr 1.2fr 1fr 0.8fr 0.8fr;
        gap: 8px;
        margin-bottom: 8px;
        align-items: center;

        span {
          text-align: center;
          font-size: 14px;
          color: #333;
          padding: 4px;

          &.rank {
            font-weight: 500;
          }

          &.red {
            color: #EB0000;
            font-weight: 600;
          }
        }
      }
    }
  }

  // 历史交锋样式
  .history-section {
    border-radius: 8px;
    padding: 15px;

    .history-header {
      display: flex;
      flex-direction: column;
      margin-bottom: 15px;
      gap: 15px;

      .top {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .logo {
          display: flex;
          align-items: center;
        }
      }

      .team-logo {
        width: 24px;
        height: 24px;
        display: block;
        margin-right: 6px;
      }

      .team-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }

      .stats {
        display: flex;
        align-items: center;
        gap: 26px;

        .stat-item {
          font-size: 14px;
          color: #333;
          text-align: center;

          .label {
            font-size: 12px;
          }

          .number {
            font-size: 16px;
            color: #FF2F2E;
          }
        }
      }

      .switch-buttons {
        display: flex;
        border-radius: 30px;
        background: #F5F5F5;
        // padding: 4px;

        .switch-btn {
          color: #666;
          font-size: 12px;
          padding: 4px 8px;
          border-radius: 30px;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: #E8E8E8;
          }

          &.active {
            background: #EB0000;
            color: white;
            border-color: #EB0000;
          }
        }
      }
    }

    .history-matches {
      min-width: 500px;

      .match-item {
        display: grid;
        grid-template-columns: 1fr 0.8fr 0.8fr 0.8fr 0.6fr 0.6fr 0.6fr;
        gap: 8px;
        padding: 8px 0;
        border-bottom: 1px solid #F0F0F0;
        align-items: center;

        &:first-child {
          background: #F5F5F5;
          font-weight: 600;
          color: #333;
        }

        &:last-child {
          border-bottom: none;
        }

        span {
          text-align: center;
          font-size: 12px;
          color: #333;

          &.red {
            color: #EB0000;
          }

          &.green {
            color: #18A058;
          }

          .blue {
            color: #3177FD;
          }
        }

        .red {
          color: #EB0000;
        }

        .green {
          color: #18A058;
        }

        .blue {
          color: #3177FD;
        }
      }
    }
  }

  // 近期战绩样式
  .recent-records {
    display: flex;
    gap: 20px;

    .record-table {
      flex: 1;
      // border: 1px solid #E8E8E8;
      border-radius: 8px;
      padding: 15px;

      .history-header {
        display: flex;
        flex-direction: column;
        margin-bottom: 15px;
        gap: 15px;

        .top {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .logo {
            display: flex;
            align-items: center;
          }
        }

        .team-logo {
          width: 24px;
          height: 24px;
          display: block;
          margin-right: 6px;
        }

        .team-name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        .stats {
          display: flex;
          align-items: center;
          gap: 26px;

          .stat-item {
            font-size: 14px;
            color: #333;
            text-align: center;

            .label {
              font-size: 12px;
            }

            .number {
              font-size: 16px;
              color: #FF2F2E;
            }
          }
        }

        .switch-buttons {
          display: flex;
          border-radius: 30px;
          background: #F5F5F5;
          // padding: 4px;

          .switch-btn {
            color: #666;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 30px;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
              background: #E8E8E8;
            }

            &.active {
              background: #EB0000;
              color: white;
              border-color: #EB0000;
            }
          }
        }
      }

      .record-matches {
        min-width: 500px;

        .match-header {
          display: grid;
          grid-template-columns: 1fr 0.8fr 0.8fr 0.8fr 0.6fr 0.6fr 0.6fr;
          gap: 8px;
          padding: 8px 0;
          background: #F5F5F5;
          font-weight: 600;
          color: #333;
          margin-bottom: 5px;

          span {
            text-align: center;
            font-size: 12px;
          }
        }

        .match-row {
          display: grid;
          grid-template-columns: 1fr 0.8fr 0.8fr 0.8fr 0.6fr 0.6fr 0.6fr;
          gap: 8px;
          padding: 8px 0;
          border-bottom: 1px solid #F0F0F0;
          align-items: center;

          &:last-child {
            border-bottom: none;
          }

          span {
            text-align: center;
            font-size: 12px;
            color: #333;

            &.red {
              color: #EB0000;
            }

            &.green {
              color: #18A058;
            }
          }

          .red {
            color: #EB0000;
          }

          .green {
            color: #18A058;
          }

          .blue {
            color: #3177FD;
          }
        }
      }
    }
  }

  // 伤停情况样式
  .injury-section {
    display: flex;
    gap: 20px;
    padding: 15px;

    .injury-table {
      flex: 1;
      border-radius: 8px;

      .injury-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        .team-logo {
          width: 24px;
          height: 24px;
          margin-right: 8px;
        }

        .team-name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }

      .injury-list {
        .injury-header-row {
          display: grid;
          grid-template-columns: 1fr 160px 160px;
          gap: 15px;
          padding: 8px 0;
          background: #F5F5F5;
          font-weight: 600;
          color: #333;
          margin-bottom: 5px;

          span {
            text-align: center;
            font-size: 12px;

            &:first-child {
              text-align: left;
              padding-left: 10px;
            }
          }
        }

        .injury-row {
          display: grid;
          grid-template-columns: 1fr 160px 160px;
          gap: 15px;
          padding: 8px 0;
          border-bottom: 1px solid #F0F0F0;
          align-items: center;

          &:last-child {
            border-bottom: none;
          }

          .player-info {
            display: flex;
            align-items: center;
            gap: 8px;
            padding-left: 10px;

            .player-avatar {
              width: 24px;
              height: 24px;
              border-radius: 50%;
            }

            .player-name {
              font-size: 12px;
              color: #333;
            }
          }

          .position,
          .reason {
            text-align: center;
            font-size: 12px;
            color: #333;
          }
        }

        .data-none {
          text-align: center;
          padding: 10px 0;
        }
      }
    }
  }

  //半全场胜负
  .half-all-section {
    border-radius: 8px;
    padding: 15px;

    .team-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;

      div {
        display: flex;
        align-items: center;
        gap: 8px;

        .team-logo {
          width: 24px;
          height: 24px;
        }

        .team-name {

          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }
    }

    .half-all-table {
      min-width: 500px;

      .table-header {
        display: grid;
        grid-template-columns: repeat(7, 1fr);

        gap: 8px;
        padding: 8px 0;
        background: #F5F5F5;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;

        span {
          text-align: center;
          font-size: 12px;
        }
      }

      .table-body {
        .table-row {
          display: grid;
          grid-template-columns: repeat(7, 1fr);
          gap: 8px;
          padding: 8px 0;
          border-bottom: 1px solid #F0F0F0;
          align-items: center;

          &:last-child {
            border-bottom: none;
          }

          span {
            text-align: center;
            font-size: 12px;
            color: #333;

            &.red {
              color: #EB0000;
            }

          }
        }
      }
    }
  }
}
</style>
