<template>
  <div class="home-page">
    <div class="live-container">
      <div class="live-content">
        <div class="video-box">
          <div class="player-box" id="dplayer-warp">
            <div id="dplayer"></div>
            <div v-if="isReconnecting" class="reconnecting-overlay">
              <div class="reconnecting-message">
                <div class="loading-spinner"></div>
                <!-- <p>连接中断，正在重新连接...</p> -->
              </div>
            </div>
            <div class="cancel-muted" v-if="isMuted" @click="toggleMute">
              <img class="icon" src="@/static/live/cancel-muted-icon.png" /> <span class="t">点击取消静音</span>
            </div>
            <div class="enter-detail-btn" @click="handleEnterDetail">
              进入直播间
            </div>
            <div class="custom-controls">
              <!-- 控制按钮区域 -->
              <div class="controls-area">
                <!-- 左侧控制组 -->
                <div class="controls-left">
                  <!-- 播放/暂停按钮 -->
                  <div class="control-btn play-btn" @click="togglePlay">
                    <svg v-if="!isPlaying" viewBox="0 0 24 24" width="20" height="20">
                      <path d="M8 5v14l11-7z" fill="currentColor" />
                    </svg>
                    <svg v-else viewBox="0 0 24 24" width="20" height="20">
                      <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" fill="currentColor" />
                    </svg>
                  </div>

                  <!-- 音量控制 -->
                  <div class="volume-control" @mouseenter="showVolumeSlider = true"
                    @mouseleave="showVolumeSlider = false">
                    <div class="control-btn volume-btn" @click="toggleMute">
                      <svg v-if="!isMuted && volume > 0.5" viewBox="0 0 24 24" width="18" height="18">
                        <path
                          d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"
                          fill="currentColor" />
                      </svg>
                      <svg v-else-if="!isMuted && volume > 0" viewBox="0 0 24 24" width="18" height="18">
                        <path
                          d="M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM5 9v6h4l5 5V4L9 9H5z"
                          fill="currentColor" />
                      </svg>
                      <svg v-else viewBox="0 0 24 24" width="18" height="18">
                        <path
                          d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"
                          fill="currentColor" />
                      </svg>
                    </div>
                    <div class="volume-slider" v-show="showVolumeSlider">
                      <div class="volume-track" @click="handleVolumeClick" ref="volumeTrackRef">
                        <div class="volume-fill" :style="{ height: (isMuted ? 0 : volume * 100) + '%' }"></div>
                        <div class="volume-thumb" :style="{ bottom: (isMuted ? 0 : volume * 100) + '%' }"
                          @mousedown="handleVolumeDrag"></div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 样式全屏按钮 -->
                <div class="control-btn fullscreen-btn" @click="toggleFullscreen">
                  <svg v-if="!isFullscreen" viewBox="0 0 24 24" width="24" height="24">
                    <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"
                      fill="currentColor" />
                  </svg>
                  <svg v-else viewBox="0 0 24 24" width="24" height="24">
                    <path d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"
                      fill="currentColor" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="room-list">
          <ul>
            <li v-for="(item, index) in homeLiveList" :key="item.id" @click="onSwitchLive(index)">
              <div class="room" :class="{ active: index === liveCurrent }">
                <i class="left-arrow"></i>
                <img class="cover" :src="item.liveCover">
                <div class="title">{{ item.liveTitle }}</div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="home-container" v-if="hotLiveList.length > 0">
      <!-- 正在热播 -->
      <div class="hot-section">
        <div class="public-title">
          <p class="title">正在热播</p>
          <div class="more" v-if="hotLiveList.length > 0" @click="handleMore('all')">查看更多 <img class="icon"
              src="@/static/index/arrow-right-icon.png" />
          </div>
        </div>
        <div class="list-content">
          <LiveItem :live-data="item" v-for="item in hotLiveList" :key="item.matchId" />
        </div>
      </div>
      <!-- 足球直播 -->
      <div class="football-section" v-if="footballLiveList.length > 0">
        <div class="public-title">
          <img class="icon" src="@/static/index/<EMAIL>" />
          <p class="title">足球直播</p>
          <div class="more" v-if="footballLiveList.length > 0" @click="handleMore('football')">查看更多 <img class="icon"
              src="@/static/index/arrow-right-icon.png" /></div>
        </div>
        <div class="list-content">
          <LiveItem :live-data="item" v-for="item in footballLiveList" :key="item.matchId" />
        </div>
      </div>
      <!-- 篮球直播 -->
      <div class="basketball-section" v-if="basketallLiveList.length > 0">
        <div class="public-title">
          <img class="icon" src="@/static/index/<EMAIL>" />
          <p class="title">篮球直播</p>
          <div class="more" v-if="basketallLiveList.length > 0" @click="handleMore('basketball')">查看更多 <img class="icon"
              src="@/static/index/arrow-right-icon.png" /></div>
        </div>
        <div class="list-content">
          <LiveItem :live-data="item" v-for="item in basketallLiveList" :key="item.matchId" />
        </div>
      </div>
      <!-- 英雄联盟 -->
      <div class="other-section" v-if="lovLiveList.length > 0">
        <div class="public-title">
          <img class="icon" src="@/static/index/<EMAIL>" />
          <p class="title">英雄联盟</p>
          <div class="more" v-if="lovLiveList.length > 0" @click="handleMore('lol')">查看更多 <img class="icon"
              src="@/static/index/arrow-right-icon.png" />
          </div>
        </div>
        <div class="list-content">
          <LiveItem :live-data="item" v-for="item in lovLiveList" :key="item.matchId" />
        </div>
      </div>
      <!-- DOTA -->
      <div class="other-section" v-if="dotaLiveList.length > 0">
        <div class="public-title">
          <img class="icon" src="@/static/index/<EMAIL>" />
          <p class="title">DOTA</p>
          <div class="more" v-if="dotaLiveList.length > 0" @click="handleMore('dota')">查看更多 <img class="icon"
              src="@/static/index/arrow-right-icon.png" />
          </div>
        </div>
        <div class="list-content">
          <LiveItem :live-data="item" v-for="item in dotaLiveList" :key="item.matchId" />
        </div>
      </div>
      <!-- csgo -->
      <div class="other-section" v-if="csLiveList.length > 0">
        <div class="public-title">
          <img class="icon" src="@/static/index/<EMAIL>" />
          <p class="title">CS:GO</p>
          <div class="more" v-if="csLiveList.length > 0" @click="handleMore('csgo')">查看更多 <img class="icon"
              src="@/static/index/arrow-right-icon.png" />
          </div>
        </div>
        <div class="list-content">
          <LiveItem :live-data="item" v-for="item in csLiveList" :key="item.matchId" />
        </div>
      </div>
      <!-- 王者荣耀 -->
      <div class="other-section" v-if="hokLiveList.length > 0">
        <div class="public-title">
          <img class="icon" src="@/static/index/<EMAIL>" />
          <p class="title">王者荣耀</p>
          <div class="more" v-if="hokLiveList.length > 0" @click="handleMore('hok')">查看更多 <img class="icon"
              src="@/static/index/arrow-right-icon.png" />
          </div>
        </div>
        <div class="list-content">
          <LiveItem :live-data="item" v-for="item in hokLiveList" :key="item.matchId" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Player from 'xgplayer';
import HlsPlugin from 'xgplayer-hls';
// import FlvPlugin from 'xgplayer-flv'
import { useUserStore } from '@/stores/user'
import 'xgplayer/dist/index.min.css';
import { useRouter } from 'vue-router';
import { getLivePageApi, getLivePageByTypeApi } from '@/api/index'
// import { isSafari } from '@/utils/common';
const userStore = useUserStore()
const router = useRouter()
// 播放器实例和 HLS 实例的引用
let player: any = null;
let hlsInstance: any = null;
//直播配置
const isPlaying = ref(false)
const isReconnecting = ref(false)
const volume = ref(0.6)
const isMuted = ref(false)
const showVolumeSlider = ref(false)
const isFullscreen = ref(false)
const volumeTrackRef = ref<HTMLElement>()
const initPlayer = () => {
  const container = document.getElementById('dplayer');
  const playerWarp = document.getElementById('dplayer-warp');
  if (!container) return;

  // 播放器配置 - 禁用原生控制栏和清晰度插件
  // let url = ''
  // if (isSafari()) {
  // url = homeLiveList.value[liveCurrent.value]?.pullUrl
  // } else {
  //   url = homeLiveList.value[liveCurrent.value]?.pullUrl.replace("m3u8", "flv")
  // }
  const playerConfig = {
    el: container,
    isLive: true,
    width: '990px',
    height: '550px',
    autoplay: true,
    playsinline: true,
    playbackRate: false,
    autoplayMuted: userStore.isFirstEnter,
    // autoplayMuted: false,
    fluid: true,
    lang: 'zh-cn',
    url: homeLiveList.value[liveCurrent.value]?.pullUrl,
    controls: false,
    // 禁用原生控制栏和清晰度插件
    ignores: ['controls', 'definition', 'progress', 'play', 'volume', 'fullscreen', 'cssfullscreen', 'loading', 'enter'],
    fullscreenTarget: playerWarp as HTMLElement,
  }
  if (document.createElement('video').canPlayType('application/vnd.apple.mpegurl')) {
    player = new Player(playerConfig)
  } else if (HlsPlugin.isSupported()) { // 第一步
    player = new Player({
      ...playerConfig,
      plugins: [HlsPlugin]
    })
  }
  // if (isSafari()) {

  // } else {
  //   player = new Player({
  //     ...playerConfig,
  //     controls: {
  //       autoHide: false
  //     },
  //     plugins: [FlvPlugin],
  //   })
  // }
  // 添加事件监听
  if (player) {
    // 监听播放器就绪事件
    player.on('ready', () => {
      console.log('播放器初始化完成')
      // 初始化状态
      volume.value = player.volume || 0.6;
      isMuted.value = player.muted || false;
      userStore.setFirstEnter(false)
    })

    // 监听播放状态
    player.on('play', () => {
      isPlaying.value = true
    })

    player.on('pause', () => {
      isPlaying.value = false
    })

    player.on('error', (error) => {
      // isReconnecting.value = false
      console.log('error---------', error)
    })

    // 监听音量变化
    player.on('volumechange', () => {
      volume.value = player.volume || 0
      isMuted.value = player.muted || false
    })

    // 监听全屏状态变化
    player.on('fullscreen_change', (data: any) => {
      isFullscreen.value = data
    })

    player.on('loadstart', (data: any) => {
      // console.log('LOAD_START')
      isReconnecting.value = true
    })
    player.on('loadeddata', (data: any) => {
      // console.log('LOADED_DATA22')
      isReconnecting.value = false
    })
    // 监听播放器聚焦
    // player.on(Events.PLAYER_FOCUS, (data: any) => {
    //   console.log(data, '--------------------')
    // })
  }
}

const switchVideo = (newUrl: string) => {
  try {
    player.switchURL(newUrl)
    player.muted = false
    isMuted.value = false
  } catch (error) {
    console.error('切换视频失败:', error);
  }
};
const liveCurrent = ref(0)


//点击进入直播间
const handleEnterDetail = () => {
  let room = homeLiveList.value[liveCurrent.value];
  router.push(`/room?matchId=${room.matchId}&userId=${room.userId}&liveTypeEnum=${room.liveType}`)
}
// 优化的房间切换函数 -
const onSwitchLive = (index: number) => {
  if (index === liveCurrent.value) {
    console.log('已经是当前直播间');
    return;
  }
  const targetRoom = homeLiveList.value[index];
  if (!targetRoom) {
    console.error('直播间不存在');
    return;
  }
  liveCurrent.value = index;
  if (player) {
    switchVideo(targetRoom.pullUrl);
  } else {
    // 如果播放器未初始化，则初始化播放器
    initPlayer();
  }
}
// 播放/暂停控制
const togglePlay = () => {
  if (!player) return

  if (isPlaying.value) {
    player.pause()
  } else {
    player.play()
  }
}
// 音量控制
const toggleMute = () => {
  if (!player) return

  if (isMuted.value) {
    player.muted = false
    isMuted.value = false
  } else {
    player.muted = true
    isMuted.value = true
  }
}
// 处理音量点击
const handleVolumeClick = (event: MouseEvent) => {
  if (!volumeTrackRef.value || !player) return

  const rect = volumeTrackRef.value.getBoundingClientRect()
  const y = event.clientY - rect.top
  const height = rect.height
  const newVolume = Math.max(0, Math.min(1, 1 - (y / height)))

  volume.value = newVolume
  player.volume = newVolume

  if (newVolume > 0) {
    isMuted.value = false
    player.muted = false
  }
}
// 处理音量拖拽
const handleVolumeDrag = (event: MouseEvent) => {
  event.preventDefault()

  const handleMouseMove = (e: MouseEvent) => {
    if (!volumeTrackRef.value || !player) return

    const rect = volumeTrackRef.value.getBoundingClientRect()
    const y = e.clientY - rect.top
    const height = rect.height
    const newVolume = Math.max(0, Math.min(1, 1 - (y / height)))

    volume.value = newVolume
    player.volume = newVolume

    if (newVolume > 0) {
      isMuted.value = false
      player.muted = false
    }
  }
  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}
// 全屏控制
const toggleFullscreen = () => {
  if (!player) return

  if (isFullscreen.value) {
    // player.exitCssFullscreen()
    player.exitFullscreen()
  } else {
    // player.getCssFullscreen()
    player.getFullscreen()
  }
}
const hotLiveList = ref<any[]>([])
//正在直播列表截取前4条
const homeLiveList = ref<any[]>([])
const getLivePage = async () => {
  let res = await getLivePageApi({ current: 1, size: 20 })
  hotLiveList.value = res.data.records;
  homeLiveList.value = res.data.records.slice(0, 4);
  // 找到第一个有拉流地址的直播间
  for (let i = 0; i < homeLiveList.value.length; i++) {
    if (homeLiveList.value[i]?.pullUrl) {
      liveCurrent.value = i;
      break;
    }
  }
  nextTick(() => {
    initPlayer()
  });
}

const footballLiveList = ref<any[]>([])
// 获取足球直播列表
const getFootballLiveList = async () => {
  let res = await getLivePageByTypeApi({
    liveTypeEnum: 'foot'
  })
  footballLiveList.value = res.data.slice(0, 8)
}

const basketallLiveList = ref<any[]>([])
// 获取篮球直播列表
const getBaskballLiveList = async () => {
  let res = await getLivePageByTypeApi({
    liveTypeEnum: 'basket'
  })
  basketallLiveList.value = res.data.slice(0, 8)
}

const lovLiveList = ref<any[]>([])
// 获取英雄联盟直播列表
const getLolLiveList = async () => {
  let res = await getLivePageByTypeApi({
    liveTypeEnum: 'lol'
  })
  lovLiveList.value = res.data.slice(0, 4)
}

const dotaLiveList = ref<any[]>([])
// 获取DOTA直播列表
const getDotaLiveList = async () => {
  let res = await getLivePageByTypeApi({
    liveTypeEnum: 'dota'
  })
  dotaLiveList.value = res.data.slice(0, 4)
}
const csLiveList = ref<any[]>([])
// 获取CS:GO直播列表
const getCsLiveList = async () => {
  let res = await getLivePageByTypeApi({
    liveTypeEnum: 'csgo'
  })
  csLiveList.value = res.data.slice(0, 4)
}
const hokLiveList = ref<any[]>([])
// 获取王者荣耀直播列表
const getHokLiveList = async () => {
  let res = await getLivePageByTypeApi({
    liveTypeEnum: 'hok'
  })
  hokLiveList.value = res.data.slice(0, 4)
}


onMounted(() => {
  getLivePage()
  getFootballLiveList()
  getBaskballLiveList()
  getLolLiveList()
  getDotaLiveList()
  getCsLiveList()
  getHokLiveList()
})

// 查看更多
const handleMore = (value: string) => {
  switch (value) {
    case 'all':
      router.push('/all?type=all')
      break
    case 'football':
      router.push('/all?type=foot')
      break
    case 'basketball':
      router.push('/all?type=basket')
      break
    case 'lol':
      router.push('/all?type=lol')
      break
    case 'dota':
      router.push('/all?type=dota')
      break
    case 'csgo':
      router.push('/all?type=csgo')
      break
    case 'hok':
      router.push('/all?type=hok')
      break
    default:
      break
  }
}

// 组件卸载时清理资源
onUnmounted(() => {
  // stopHeartbeat();
  if (hlsInstance) {
    hlsInstance.destroy();
    hlsInstance = null;
  }
  if (player) {
    player.destroy() // 销毁播放器
    player = null // 将实例引用置空
  }
})
</script>

<style lang="scss" scoped>
.public-title {
  display: flex;
  align-items: center;

  .icon {
    width: 36px;
    height: 36px;
    display: block;
    margin-right: 12px;
  }

  .title {
    font-size: 24px;
    color: #333;
    font-weight: 500;
  }

  .more {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #818181;
    font-size: 16px;
    margin-left: auto;
    line-height: 1;

    .icon {
      width: 14px;
      height: 14px;
      display: block;
      margin-left: 4px;
    }
  }

  margin-bottom: 18px;
}

.home-page {
  width: 100%;
  min-height: 100vh;
  background: #F3F3F3;

  .live-container {
    height: 550px;
    background-image: url('@/static/img/bg.jpg');
    background-position: center;
    background-size: cover;

    .live-content {
      width: 1200px;
      height: 550px;
      margin: 0 auto;
      display: flex;

      .video-box {
        width: 990px;
        height: 550px;
        position: relative;

        #dplayer {
          width: 100%;
          height: 100%;
        }

        .player-box {
          width: 100%;
          height: 100%;
          position: relative;

          &.xgplayer-fullscreen-parent {
            position: fixed;
          }

          .xgplayer-controls {
            display: none;
          }

          .enter-detail-btn {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: 260px;
            width: 220px;
            height: 66px;
            background-color: rgba(0, 0, 0, .7);
            border: 2px solid #ea5f3d;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 901;
            cursor: pointer;
            color: #fff;
            font-size: 24px;

            &:hover {
              background-color: #FB2B1F;
              border-color: #FB2B1F;
            }
          }

          .cancel-muted {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: 90px;
            width: 180px;
            height: 56px;
            background: #ea5f3d;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 901;
            cursor: pointer;

            .t {
              margin-left: 20px;
              font-size: 14px;
              color: #fff;
            }

            .icon {
              width: 30px;
              display: block;
            }

            &:hover {
              background: #FB2B1F;
            }
          }

          .custom-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            // background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            background-color: rgba(0, 0, 0, 0.3);
            padding: 6px 16px;
            // transition: all 0.3s ease;
            z-index: 100;
            opacity: 0;
            // transform: translateY(100%);
          }

          &:hover {
            .custom-controls {
              opacity: 1;
              // transform: translateY(0);
            }

          }
        }

        .reconnecting-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 999;

          .reconnecting-message {
            text-align: center;
            color: white;

            .loading-spinner {
              width: 40px;
              height: 40px;
              border: 4px solid #f3f3f3;
              border-top: 4px solid #ffc71c;
              border-radius: 50%;
              animation: spin 1s linear infinite;
              margin: 0 auto 15px;
            }

            p {
              font-size: 16px;
              margin: 0;
            }
          }
        }
      }

      .room-list {
        width: 230px;
        display: flex;
        flex-direction: column;
        padding-left: 10px;
        padding-top: 5px;
        background-color: #000;

        li {
          padding: 0 0 7px 0;
          // margin-top: 10px;

          .room {
            display: block;
            background: rgba(0, 0, 0, .7);
            cursor: pointer;
            border: 2px solid rgba(255, 255, 255, 0);
            position: relative;
            width: 100%;
            height: 130px;
            border-radius: 6px;
            // overflow: hidden;

            .left-arrow {
              width: 13px;
              height: 11px;
              position: absolute;
              left: -15px;
              top: 50%;
              transform: translateY(-50%);
              display: none;

              &::after {
                position: absolute;
                content: '';
                border-top: 6px transparent dashed;
                border-left: 7px transparent dashed;
                border-bottom: 6px transparent dashed;
                border-right: 7px #FB2B1F solid;
              }
            }

            .cover {
              width: 100%;
              height: 100%;
              border-radius: 6px;
            }

            &:hover {
              border: 2px solid #FB2B1F;

              .left-arrow {
                display: block;
              }

              .title {
                display: block;
              }
            }

            &.active {
              border: 2px solid #FB2B1F;

              .left-arrow {
                display: block;
              }

              .title {
                display: block;
              }
            }

            .title {
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;
              background: rgba(0, 0, 0, 0.7);
              color: #fff;
              font-size: 12px;
              overflow: hidden;
              white-space: nowrap;
              display: none;
              padding-left: 5px;
              border-bottom-left-radius: 6px;
              border-bottom-right-radius: 6px;
            }
          }
        }
      }
    }
  }

  .home-container {
    width: 1200px;
    margin: 0 auto;
    padding: 40px 0px;

    .hot-section {
      position: relative;
      overflow: hidden;

      // .custom-arrow {
      //   display: flex;
      //   position: absolute;
      //   top: -50px;
      //   right: 10px;

      //   .custom-arrow--left {
      //     margin-right: 20px;
      //     cursor: pointer;
      //   }

      //   .custom-arrow--right {
      //     cursor: pointer;
      //   }

      //   img {
      //     display: block;
      //     width: 32px;
      //     height: 32px;
      //   }
      // }
    }

    :deep(.n-carousel) {
      overflow: visible;
    }

    .hot-section {
      // margin-top: 40px;

      .list-content {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
      }
    }

    .football-section {
      margin-top: 40px;

      .list-content {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
      }
    }

    .basketball-section {
      margin-top: 40px;

      .list-content {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
      }
    }

    .other-section {
      margin-top: 40px;

      .list-content {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
      }
    }
  }
}

.none-box {
  background-color: #fff;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 控制按钮区域 */
.controls-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.controls-left,
.controls-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #fff;
  backdrop-filter: blur(8px);
}

.play-btn:hover {
  background: rgba(255, 255, 255, 0.25);
}

/* 音量控制 */
.volume-control {
  position: relative;
  display: flex;
  align-items: center;
}

.volume-slider {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  border-radius: 20px;
  padding: 12px 8px;
  backdrop-filter: blur(12px);
}

.volume-track {
  position: relative;
  width: 4px;
  height: 80px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  cursor: pointer;
}

.volume-fill {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(0deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  transition: height 0.2s ease;
}

.volume-thumb {
  position: absolute;
  left: 50%;
  width: 12px;
  height: 12px;
  background: #fff;
  border-radius: 50%;
  transform: translateX(-50%);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.volume-thumb:hover {
  transform: translateX(-50%) scale(1.2);
}

/* 清晰度控制 */
.definition-control {
  position: relative;
}

.definition-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  color: #fff;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
  min-width: 50px;
  justify-content: center;
}

.definition-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.definition-list {
  position: absolute;
  bottom: 100%;
  right: 0;
  margin-bottom: 8px;
  background: rgba(0, 0, 0, 0.95);
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(12px);
  min-width: 110px;
}

.definition-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 14px;
  color: #fff;

  font-size: 13px;
  cursor: pointer;
  transition: all 0.25s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);

  .login-tag {
    font-size: 12px;
    color: #FB2B1F;
  }
}

.definition-item:last-child {
  border-bottom: none;
}

.definition-item:hover {
  background: rgba(255, 255, 255, 0.12);
}

.definition-item.selected {
  background-color: #FB2B1F;
  color: #fff;
  font-weight: 600;
}



.check-icon {
  color: #fff;
  font-weight: bold;
  font-size: 14px;
}


/* 移动端适配 */
@media (max-width: 768px) {
  .custom-controls {
    padding: 16px 12px 12px;
  }

  .controls-left,
  .controls-right {
    gap: 8px;
  }

  .control-btn {
    width: 32px;
    height: 32px;
  }

  .play-btn {
    width: 40px;
    height: 40px;
  }

  .definition-btn {
    padding: 6px 10px;
    font-size: 12px;
    min-width: 45px;
  }

  .volume-slider {
    display: none;
    /* 移动端隐藏音量滑块 */
  }

  .time-display {
    font-size: 11px;
  }
}
</style>
