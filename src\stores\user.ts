import { defineStore } from 'pinia'
import { loginOutApi, getBaseUserInfoApi } from '@/api/user'

interface UserStateType {
  // 用户token
  userToken: string
  // 用户信息
  userInfo: UserInfoType
  // 是否第一次进入
  isFirstEnter: boolean
}
interface UserInfoType {
  id: number
  userName: string
  userImage: string
  sex: string
  birthday: string
  phoneNum: string
  diamond: number,
  isAuthor: number,//0.不是 1.是主播
  exp: number,//经验
  grade: number,//等级
  ticket: number,//球票
}

export const useUserStore = defineStore('userStore', {
  state: (): UserStateType => {
    return {
      userToken: '',
      isFirstEnter: true,
      userInfo: {
        id: 0,
        userName: '',
        userImage: '',
        sex: '',
        birthday: '',
        phoneNum: '',
        diamond: 0,
        isAuthor: 0,
        exp: 0,
        grade: 0,
        ticket: 0
      }
    }
  },
  // persist: true, // 全部持久化
  persist: {
    paths: ['userToken', 'userInfo']
  },
  getters: {
    // 获取用户是否已登录
    isLogin: (state) => {
      return !!state.userToken
    },
    // 获取用户token
    getToken: (state) => {
      return state.userToken
    },
    // 获取用户信息
    getUserInfo: (state) => {
      return state.userInfo
    },
    // 获取用户是否为主播
    isAuthor: (state) => {
      return state.userInfo.isAuthor === 1
    },
    // 获取用户等级
    getUserGrade: (state) => {
      return state.userInfo.grade
    },
    // 获取用户经验值
    getUserExp: (state) => {
      return state.userInfo.exp
    }
  },
  actions: {
    // 设置用户token
    setUserToken(token: string) {
      this.userToken = token
    },
    // 设置用户信息
    setUserInfo(userInfo: UserInfoType) {
      this.userInfo = userInfo
    },
    // 设置完整的用户数据（登录时使用）
    setUserData(token: string, userInfo: UserInfoType) {
      this.setUserToken(token)
      this.setUserInfo(userInfo)
    },
    // 清除用户数据（退出登录时使用）
    clearUserData() {
      this.userToken = ''
      this.userInfo = {
        id: 0,
        userName: '',
        userImage: '',
        sex: '',
        birthday: '',
        phoneNum: '',
        diamond: 0,
        isAuthor: 0,
        exp: 0,
        grade: 0,
        ticket: 0,
      }
    },
    // 用户退出登录
    async loginOut() {
      try {
        await loginOutApi()
        this.clearUserData()
      } catch (error) {
        console.error('退出登录失败:', error)
      }
    },
    // 更新用户信息（从API获取最新数据）
    async updateUserInfo() {
      try {
        const data: any = await getBaseUserInfoApi()
        this.setUserInfo(data.data)
      } catch (error) {
        console.error('更新用户信息失败:', error)
      }
    },
    setFirstEnter(value: boolean) {
      this.isFirstEnter = value
    }
  }
})
